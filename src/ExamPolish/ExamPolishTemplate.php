<?php

namespace DD\App\ExamPolish;

use DD\App\ExamPolish\Config;

class ExamPolishTemplate
{
    private function formatDate(string $date): string
    {
        if (!$date) {
            return '';
        }

        $dateObj = \DateTime::createFromFormat('Y-m-d', $date);
        return $dateObj ? $dateObj->format('d.m.Y') : '';
    }

    public function renderTabRegistration(ExamPolishData $data): void
    {
        $exams = $data->getRepeater(Config::TAB_REGISTRATION, Config::FIELD_EXAM_LIST);
        $options = $data->getTabData(Config::TAB_REGISTRATION);
        $exam_date = $options[Config::FIELD_EXAM_DATE] ?? '';

        $formatted_date = $this->formatDate($options['exam_date'] ?? '');

        if (empty($exams)) {
            echo '<p>' . __('Brak dostępnych egzaminów', DD_TEXTDOMAIN) . '</p>';
            return;
        }

        echo '<h2 class="exam-title">'
            . '<span class="color-secondary">' . __('Rejestracja na egzamin z języka polskiego ', DD_TEXTDOMAIN) . '</span> '
            . __('dla obcokrajowców', DD_TEXTDOMAIN) . ' '
            . esc_html($formatted_date)
            . '</h2><hr>';

        foreach ($exams as $exam) {
            ?>
            <div class="exam-item">
                <div class="wrapper">
                    <h3 class="exam-name"><?php _e('Rejestracja na  ', DD_TEXTDOMAIN); ?><span class="color-secondary"><?= esc_html($exam['name'] ?? '') ?></span></h3>
                    <span class="status <?= !empty($exam['active']) ? 'active' : 'inactive' ?>">
                        <?= !empty($exam['active']) ? __('Otwarta', DD_TEXTDOMAIN) : __('Zamknięta', DD_TEXTDOMAIN) ?>
                    </span>
                </div>
                <div class="description"><?= wp_kses_post($exam['description'] ?? '') ?></div>
                <?php if (!empty($exam['link'])): ?>
                    <a href="<?= esc_url($exam['link']) ?>" class="button"><?= __('Zapisz się na egzamin', DD_TEXTDOMAIN) ?></a>
                <?php endif; ?>
            </div>
            <hr>
            <?php
        }
    }

    public function renderTabDatesAndFees(ExamPolishData $data): void
    {
        $tab = Config::TAB_DATES_FEES;
        $options = $data->getTabData($tab);

        echo '<h2 class="section-title">' . __('Terminy ', DD_TEXTDOMAIN) . '<span class="color-secondary">' . __('i opłaty', DD_TEXTDOMAIN) . '</span>' . '</h2>';

        $this->renderTable(
            $data->getRepeater($tab, 'table1'),
            $options['table1_header'] ?? '',
            ['date', 'day', 'level', 'group'],
            [
                __('Data egzaminu', DD_TEXTDOMAIN),
                __('Dzień tygodnia', DD_TEXTDOMAIN),
                __('Poziom', DD_TEXTDOMAIN),
                __('Grupa', DD_TEXTDOMAIN),
            ]
        );

        if (!empty($options['text2b'])) {
            echo '<div class="exam-text">' . wpautop(wp_kses_post($options['text2b'])) . '</div>';
        }

        echo '<div class="exam-image-text">';
        echo '<img src="' . get_template_directory_uri() . '/assets/img/exampage-graphs.webp" alt="' . esc_attr(__('Informacje o egzaminie', DD_TEXTDOMAIN)) . '">';
        echo '<p>' . __('Na przestrzeni lat, kursanci naszej szkoły osiągają spektakularne wyniki z egzaminu. 
        Procent zdawalności na egzaminie z języka polskiego w naszym ośrodku nigdy nie spadł poniżej 90%. Więcej informacji o szkoleniu pre-egzaminacyjnym i kursach przygotowawczych znajdziesz w dedykowanych im zakładkach.', DD_TEXTDOMAIN) . '</p>';
        echo '</div>';

        $this->renderTable(
            $data->getRepeater($tab, 'table2'),
            $options['table2_header'] ?? '',
            ['price', 'type', 'level', 'fee'],
            [
                __('Cena', DD_TEXTDOMAIN),
                __('Rodzaj opłaty', DD_TEXTDOMAIN),
                __('Poziom', DD_TEXTDOMAIN),
                __('Opłata', DD_TEXTDOMAIN),
            ]
        );

        if (!empty($options['text2d'])) {
            echo '<div class="exam-text">' . wpautop(wp_kses_post($options['text2d'])) . '</div>';
        }
    }

    public function renderTabPreExamTraining(ExamPolishData $data): void
    {
        $tab = Config::TAB_PRE_EXAM_TRAINING;
        $options = $data->getTabData($tab);

        if (!empty($options['pre_exam_training_content_left']) || !empty($options['pre_exam_training_content_right'])) {
            echo '<div class="pre-exam-training-content">';
            echo '<div class="left-content">' . wpautop(wp_kses_post(__($options['pre_exam_training_content_left'] ?? '', DD_TEXTDOMAIN))) . '</div>';
            echo '<div class="right-content">' . wpautop(wp_kses_post(__($options['pre_exam_training_content_right'] ?? '', DD_TEXTDOMAIN))) . '</div>';
            echo '</div>';
        }

        $this->renderTable(
            $data->getRepeater($tab, 'table3'),
            $options['table3_header'] ?? '',
            ['level', 'type', 'date', 'price', 'link'],
            [
                __('Poziom szkolenia', DD_TEXTDOMAIN),
                __('Rodzaj szkolenia', DD_TEXTDOMAIN),
                __('Termin', DD_TEXTDOMAIN),
                __('Cena', DD_TEXTDOMAIN),
                __('', DD_TEXTDOMAIN),
            ]
        );

        if (!empty($options['text3c'])) {
            echo '<div class="exam-text">' . wpautop(wp_kses_post($options['text3c'])) . '</div>';
        }
    }

    public function renderTabSessionDates(ExamPolishData $data): void
    {
        $sessions = $data->getRepeater(Config::TAB_SESSION_DATES, Config::FIELD_SESSION_LIST);

        if (empty($sessions)) { 
            echo '<p>' . __('Brak dostępnych sesji egzaminacyjnych', DD_TEXTDOMAIN) . '</p>';
            return;
        }

        echo '<h2 class="exam-title">'
            . '<span class="color-secondary">' . __('Terminy', DD_TEXTDOMAIN) . '</span> '
            . __('sesji egzaminacyjnych', DD_TEXTDOMAIN)
            . '</h2><hr>';

        foreach ($sessions as $session) {
            $name = esc_html($session['session_name'] ?? '');
            $description = wp_kses_post($session['session_description'] ?? '');
            $date = $this->formatDate($session['session_date'] ?? '');

            ?>
            <div class="exam-item">
                <div class="wrapper">
                    <span class="status inactive"><?= $name ?></span>
                    <?php if ($date): ?>
                        <h3 class="exam-name"><?= __('Sesja egzaminacyjna - ', DD_TEXTDOMAIN) ?><span class="color-secondary"><?= esc_html($date) ?></span></h3>
                    <?php endif; ?>
                </div>
                <div class="description"><?= $description ?></div>
            </div>
            <hr>
            <?php
        }
    }

    private function renderTable(array $rows, string $tableTitle = '', array $keys = [], array $labels = []): void
    {
        if (empty($rows) || empty($keys) || empty($labels)) {
            return;
        }

        ?>
        <div class="exam-table-section">
            <table>
                <thead>
                    <?php if (!empty($tableTitle)): ?>
                        <tr>
                            <th colspan="<?= count($labels) ?>" class="table-title"><?= esc_html(__($tableTitle, DD_TEXTDOMAIN)) ?></th>
                        </tr>
                    <?php endif; ?>
                    <tr>
                        <?php foreach ($labels as $label): ?>
                            <th><?= esc_html($label) ?></th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($rows as $row): ?>
                        <tr>
                            <?php foreach ($keys as $key): ?>
                                <td>
                                    <?php if ($key === 'link' && !empty($row[$key])): ?>
                                        <a class="button" href="<?= esc_url($row[$key]) ?>" target="_blank" rel="noopener noreferrer"><?= __('Kup szkolenie ', DD_TEXTDOMAIN) ?><?php get_template_part( 'templates/parts/black-arrow-right' ); ?></a>
                                    <?php else: ?>
                                        <?= esc_html(__($row[$key] ?? '', DD_TEXTDOMAIN)) ?>
                                    <?php endif; ?>
                                </td>
                            <?php endforeach; ?>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php
    }
}